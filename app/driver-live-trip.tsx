import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Dimensions,
    FlatList,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';

const { width, height } = Dimensions.get('window');

// Passenger data
const PASSENGERS = [
  { id: 1, name: 'AHMAD', status: 'picked_up', avatar: 'A' },
  { id: 2, name: 'ALI', status: 'waiting', avatar: 'A' },
  { id: 3, name: 'SARA', status: 'picked_up', avatar: 'S' },
  { id: 4, name: 'OMAR', status: 'waiting', avatar: 'O' },
  { id: 5, name: 'FATIMA', status: 'picked_up', avatar: 'F' },
  { id: 6, name: 'HASSAN', status: 'waiting', avatar: 'H' },
];

// Trip route data
const TRIP_ROUTE = [
  { 
    latitude: 34.0209, 
    longitude: -6.8416,
    title: 'نقطة البداية'
  },
  { 
    latitude: 34.0250, 
    longitude: -6.8350,
    title: 'محطة وسطية'
  },
  { 
    latitude: 34.0300, 
    longitude: -6.8200,
    title: 'جامعة العلوم'
  },
];

export default function DriverLiveTripScreen() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(1800); // 30 minutes in seconds
  const [currentDistance, setCurrentDistance] = useState(60); // 60KM
  const [tripProgress, setTripProgress] = useState(0.4); // 40% progress

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 0) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Format countdown time
  const formatCountdown = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleGoPress = () => {
    Alert.alert(
      'متابعة الرحلة',
      'هل تريد متابعة إلى المحطة التالية؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'متابعة',
          onPress: () => {
            // Update progress
            setTripProgress(prev => Math.min(prev + 0.2, 1));
            setCurrentDistance(prev => Math.max(prev - 15, 0));
            // Navigate to vehicle info screen
            router.push('/driver-vehicle-info');
          }
        }
      ]
    );
  };

  const handlePassengerPress = (passenger: any) => {
    const newStatus = passenger.status === 'waiting' ? 'picked_up' : 'waiting';
    Alert.alert(
      'تحديث حالة الطالب',
      `تغيير حالة ${passenger.name} إلى ${newStatus === 'picked_up' ? 'تم الاستلام' : 'في الانتظار'}؟`,
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'تأكيد',
          onPress: () => {
            // Update passenger status logic would go here
            console.log(`Updated ${passenger.name} status to ${newStatus}`);
          }
        }
      ]
    );
  };

  const renderPassenger = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.passengerItem}
      onPress={() => handlePassengerPress(item)}
      activeOpacity={0.7}
    >
      <View style={[
        styles.passengerAvatar,
        { backgroundColor: item.status === 'picked_up' ? Colors.light.success : Colors.light.warning }
      ]}>
        <Text style={styles.passengerAvatarText}>{item.avatar}</Text>
      </View>
      <View style={styles.passengerInfo}>
        <Text style={styles.passengerName}>{item.name}</Text>
        <Text style={[
          styles.passengerStatus,
          { color: item.status === 'picked_up' ? Colors.light.success : Colors.light.warning }
        ]}>
          {item.status === 'picked_up' ? 'تم الاستلام' : 'في الانتظار'}
        </Text>
      </View>
      <View style={[
        styles.statusIndicator,
        { backgroundColor: item.status === 'picked_up' ? Colors.light.success : Colors.light.warning }
      ]} />
    </TouchableOpacity>
  );

  const mapRegion = {
    latitude: 34.0255,
    longitude: -6.8300,
    latitudeDelta: 0.02,
    longitudeDelta: 0.02,
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.light.primary} />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backButtonIcon}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>العد التنازلي</Text>
          <Text style={styles.headerSubtitle}>Countdown</Text>
        </View>
        <View style={styles.countdownContainer}>
          <Text style={styles.countdownText}>{formatCountdown(countdown)}</Text>
        </View>
      </View>

      {/* Map Section */}
      <View style={styles.mapSection}>
        <View style={styles.mapContainer}>
          <MapView
            style={styles.map}
            provider={PROVIDER_GOOGLE}
            initialRegion={mapRegion}
            showsUserLocation={true}
            showsMyLocationButton={false}
            showsCompass={false}
            toolbarEnabled={false}
          >
            {/* Route Polyline */}
            <Polyline
              coordinates={TRIP_ROUTE}
              strokeColor={Colors.light.primary}
              strokeWidth={4}
            />
            
            {/* Route Markers */}
            {TRIP_ROUTE.map((location, index) => (
              <Marker
                key={index}
                coordinate={location}
                pinColor={
                  index === 0 ? Colors.light.success : 
                  index === TRIP_ROUTE.length - 1 ? Colors.light.error : 
                  Colors.light.warning
                }
                title={location.title}
              />
            ))}
          </MapView>
          
          {/* Map Controls Overlay */}
          <View style={styles.mapControls}>
            <TouchableOpacity style={styles.goButton} onPress={handleGoPress}>
              <Text style={styles.goButtonText}>Go</Text>
            </TouchableOpacity>
            <View style={styles.distanceIndicator}>
              <Text style={styles.distanceText}>{currentDistance}KM</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressSection}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, { width: `${tripProgress * 100}%` }]} />
        </View>
        <Text style={styles.progressText}>
          {Math.round(tripProgress * 100)}% مكتمل
        </Text>
      </View>

      {/* Passengers List */}
      <View style={styles.passengersSection}>
        <View style={styles.passengersSectionHeader}>
          <Text style={styles.passengersSectionTitle}>قائمة الطلاب</Text>
          <Text style={styles.passengersCount}>
            {PASSENGERS.filter(p => p.status === 'picked_up').length}/{PASSENGERS.length}
          </Text>
        </View>
        
        <FlatList
          data={PASSENGERS}
          renderItem={renderPassenger}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.passengersList}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 15,
    backgroundColor: Colors.light.primary,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonIcon: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  countdownContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  countdownText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  mapSection: {
    margin: 20,
    marginBottom: 10,
  },
  mapContainer: {
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: 'absolute',
    top: 15,
    right: 15,
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  goButton: {
    backgroundColor: Colors.light.success,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
    marginBottom: 10,
    shadowColor: Colors.light.success,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  goButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  distanceIndicator: {
    backgroundColor: 'rgba(59, 130, 246, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  distanceText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  progressSection: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.light.success,
    borderRadius: 4,
  },
  progressText: {
    textAlign: 'center',
    marginTop: 8,
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  passengersSection: {
    flex: 1,
    paddingHorizontal: 20,
  },
  passengersSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  passengersSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  passengersCount: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.primary,
  },
  passengersList: {
    paddingBottom: 20,
  },
  passengerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  passengerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  passengerAvatarText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  passengerInfo: {
    flex: 1,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 2,
  },
  passengerStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
});
