import DriverNavigationBar from '@/components/DriverNavigationBar';
import { useRouter } from 'expo-router';
import React from 'react';
import {
    Alert,
    Dimensions,
    FlatList,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';

const { width, height } = Dimensions.get('window');

// Student data with locations and phone numbers
const STUDENTS = [
  {
    id: 1,
    name: 'AHMAD',
    avatar: 'A',
    phone: '+212600123456',
    latitude: 34.0215,
    longitude: -6.8420,
    distanceFromCenter: 2.3
  },
  {
    id: 2,
    name: 'ALI',
    avatar: 'A',
    phone: '+212600123457',
    latitude: 34.0230,
    longitude: -6.8380,
    distanceFromCenter: 1.8
  },
  {
    id: 3,
    name: 'SARA',
    avatar: 'S',
    phone: '+212600123458',
    latitude: 34.0195,
    longitude: -6.8450,
    distanceFromCenter: 3.1
  },
  {
    id: 4,
    name: 'OMA<PERSON>',
    avatar: 'O',
    phone: '+212600123459',
    latitude: 34.0280,
    longitude: -6.8320,
    distanceFromCenter: 1.2
  },
  {
    id: 5,
    name: 'FATIMA',
    avatar: 'F',
    phone: '+212600123460',
    latitude: 34.0240,
    longitude: -6.8360,
    distanceFromCenter: 1.5
  },
  {
    id: 6,
    name: 'HASSAN',
    avatar: 'H',
    phone: '+212600123461',
    latitude: 34.0260,
    longitude: -6.8340,
    distanceFromCenter: 0.9
  },
];

// Central location (driver's current position)
const CENTRAL_LOCATION = {
  latitude: 34.0255,
  longitude: -6.8300,
};

// Original countdown reference (30 minutes)
const ORIGINAL_COUNTDOWN = '30:00';

// Trip route data
const TRIP_ROUTE = [
  { 
    latitude: 34.0209, 
    longitude: -6.8416,
    title: 'نقطة البداية'
  },
  { 
    latitude: 34.0250, 
    longitude: -6.8350,
    title: 'محطة وسطية'
  },
  { 
    latitude: 34.0300, 
    longitude: -6.8200,
    title: 'جامعة العلوم'
  },
];

export default function DriverLiveTripScreen() {
  const router = useRouter();

  const handleCallStudent = (student: any) => {
    Alert.alert(
      'اتصال بالطالب',
      `هل تريد الاتصال بـ ${student.name}؟`,
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'اتصال',
          onPress: () => {
            Linking.openURL(`tel:${student.phone}`);
          },
        },
      ]
    );
  };

  const handleGoToVehicleInfo = () => {
    router.push('/driver-vehicle-info');
  };

  const renderStudent = ({ item }: { item: any }) => (
    <LinearGradient
      colors={['#EBF4FF', '#DBEAFE']}
      style={styles.studentCard}
    >
      <View style={styles.studentInfo}>
        <LinearGradient
          colors={['#3B82F6', '#1D4ED8']}
          style={styles.studentAvatar}
        >
          <Text style={styles.studentAvatarText}>{item.avatar}</Text>
        </LinearGradient>
        <View style={styles.studentDetails}>
          <Text style={styles.studentName}>{item.name}</Text>
          <Text style={styles.studentDistance}>
            المسافة: {item.distanceFromCenter} كم
          </Text>
        </View>
      </View>
      <TouchableOpacity
        style={styles.callButton}
        onPress={() => handleCallStudent(item)}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={['#3B82F6', '#1E40AF']}
          style={styles.callButtonGradient}
        >
          <Text style={styles.callButtonText}>📞</Text>
        </LinearGradient>
      </TouchableOpacity>
    </LinearGradient>
  );

  const mapRegion = {
    latitude: CENTRAL_LOCATION.latitude,
    longitude: CENTRAL_LOCATION.longitude,
    latitudeDelta: 0.02,
    longitudeDelta: 0.02,
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1E40AF" />

      {/* Header with Gradient */}
      <LinearGradient
        colors={['#1E40AF', '#3B82F6']}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backButtonIcon}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>تتبع الطلاب المباشر</Text>
          <Text style={styles.headerSubtitle}>Live Student Tracking</Text>
        </View>
        <View style={styles.referenceContainer}>
          <Text style={styles.referenceText}>المرجع: {ORIGINAL_COUNTDOWN}</Text>
        </View>
      </LinearGradient>

      {/* Live Map Section */}
      <View style={styles.mapSection}>
        <LinearGradient
          colors={['#EBF4FF', '#DBEAFE']}
          style={styles.mapContainer}
        >
          <Text style={styles.sectionTitle}>خريطة مواقع الطلاب المباشرة</Text>
          <View style={styles.mapWrapper}>
            <MapView
              style={styles.map}
              provider={PROVIDER_GOOGLE}
              initialRegion={mapRegion}
              showsUserLocation={true}
              showsMyLocationButton={false}
              showsCompass={false}
              toolbarEnabled={false}
            >
              {/* Central Location Marker (Driver) */}
              <Marker
                coordinate={CENTRAL_LOCATION}
                pinColor="#1E40AF"
                title="موقع السائق"
                description="الموقع المركزي"
              />

              {/* Student Location Markers */}
              {STUDENTS.map((student) => (
                <Marker
                  key={student.id}
                  coordinate={{
                    latitude: student.latitude,
                    longitude: student.longitude,
                  }}
                  pinColor="#3B82F6"
                  title={student.name}
                  description={`المسافة: ${student.distanceFromCenter} كم`}
                />
              ))}
            </MapView>
          </View>
        </LinearGradient>
      </View>

      {/* Students List Section */}
      <View style={styles.studentsSection}>
        <LinearGradient
          colors={['#EBF4FF', '#DBEAFE']}
          style={styles.studentsSectionHeader}
        >
          <Text style={styles.sectionTitle}>قائمة الطلاب</Text>
          <Text style={styles.studentsCount}>{STUDENTS.length} طلاب</Text>
        </LinearGradient>

        <FlatList
          data={STUDENTS}
          renderItem={renderStudent}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.studentsList}
        />
      </View>

      {/* Vehicle Info Button */}
      <View style={styles.actionSection}>
        <TouchableOpacity
          style={styles.vehicleInfoButton}
          onPress={handleGoToVehicleInfo}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['#1E40AF', '#3B82F6']}
            style={styles.vehicleInfoButtonGradient}
          >
            <Text style={styles.vehicleInfoButtonText}>معلومات المركبة</Text>
            <Text style={styles.vehicleInfoButtonIcon}>🚗</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>

      {/* Navigation Bar */}
      <DriverNavigationBar currentScreen="live-trip" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  backButtonIcon: {
    fontSize: 22,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginTop: 2,
  },
  referenceContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  referenceText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  mapSection: {
    margin: 20,
    marginBottom: 15,
  },
  mapContainer: {
    borderRadius: 20,
    padding: 20,
    shadowColor: '#1E40AF',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 10,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.2)',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1E40AF',
    textAlign: 'center',
    marginBottom: 15,
    textShadowColor: 'rgba(30, 64, 175, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  mapWrapper: {
    height: 220,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#3B82F6',
  },
  map: {
    flex: 1,
  },
  studentsSection: {
    flex: 1,
    paddingHorizontal: 20,
  },
  studentsSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 16,
    marginBottom: 15,
    shadowColor: '#1E40AF',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.2)',
  },
  studentsCount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E40AF',
  },
  studentsList: {
    paddingBottom: 20,
  },
  studentCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: '#1E40AF',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.2)',
  },
  studentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  studentAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
    shadowColor: '#1E40AF',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  studentAvatarText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1E40AF',
    marginBottom: 4,
  },
  studentDistance: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  callButton: {
    marginLeft: 10,
  },
  callButtonGradient: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#1E40AF',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  callButtonText: {
    fontSize: 20,
  },
  actionSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  vehicleInfoButton: {
    borderRadius: 16,
    shadowColor: '#1E40AF',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 10,
  },
  vehicleInfoButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 16,
  },
  vehicleInfoButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 10,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  vehicleInfoButtonIcon: {
    fontSize: 20,
  },
});
