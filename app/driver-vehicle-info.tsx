import { Colors } from '@/constants/Colors';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Dimensions,
    FlatList,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';

const { width, height } = Dimensions.get('window');

// Vehicle information
const VEHICLE_INFO = {
  make: 'Toyota',
  model: 'Hiace',
  year: 2020,
  color: 'أبيض',
  licensePlate: 'A-12345-ب',
  capacity: 23,
  currentLoad: 4,
};

// Passenger data with confirmation status
const CONFIRMED_PASSENGERS = [
  { id: 1, name: 'AHMAD', status: 'confirmed', avatar: 'A', confirmationCode: 'C' },
  { id: 2, name: 'ALI', status: 'confirmed', avatar: 'A', confirmationCode: 'C' },
  { id: 3, name: '<PERSON><PERSON>', status: 'confirmed', avatar: 'A', confirmationCode: 'C' },
  { id: 4, name: 'SA<PERSON>', status: 'confirmed', avatar: 'S', confirmationCode: 'C' },
];

// Trip route data
const VEHICLE_ROUTE = [
  { 
    latitude: 34.0250, 
    longitude: -6.8350,
    title: 'الموقع الحالي'
  },
  { 
    latitude: 34.0300, 
    longitude: -6.8200,
    title: 'جامعة العلوم'
  },
];

export default function DriverVehicleInfoScreen() {
  const router = useRouter();
  const [currentTime, setCurrentTime] = useState('01:30');
  const [remainingDistance, setRemainingDistance] = useState(60); // 60KM
  const [tripDuration, setTripDuration] = useState(90); // 1.5 hours in minutes

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setTripDuration(prev => prev + 1);
      const hours = Math.floor(tripDuration / 60);
      const minutes = tripDuration % 60;
      setCurrentTime(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`);
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, [tripDuration]);

  const handlePassengerPress = (passenger: any) => {
    Alert.alert(
      'تفاصيل الطالب',
      `الطالب: ${passenger.name}\nالحالة: مؤكد\nرمز التأكيد: ${passenger.confirmationCode}`,
      [
        { text: 'موافق', style: 'default' },
        {
          text: 'إلغاء التأكيد',
          style: 'destructive',
          onPress: () => {
            Alert.alert('تم إلغاء التأكيد', `تم إلغاء تأكيد ${passenger.name}`);
          }
        }
      ]
    );
  };

  const handleCompleteTrip = () => {
    Alert.alert(
      'إكمال الرحلة',
      'هل أنت متأكد من إكمال الرحلة؟ سيتم إشعار جميع الطلاب بالوصول.',
      [
        { text: 'إلغاء', style: 'cancel' },
        {
          text: 'إكمال',
          style: 'default',
          onPress: () => {
            Alert.alert(
              'تم إكمال الرحلة',
              'تم إكمال الرحلة بنجاح. شكراً لك!',
              [
                {
                  text: 'موافق',
                  onPress: () => router.push('/driver-trip-tracking')
                }
              ]
            );
          }
        }
      ]
    );
  };

  const renderPassenger = ({ item }: { item: any }) => (
    <TouchableOpacity 
      style={styles.passengerItem}
      onPress={() => handlePassengerPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.passengerAvatar}>
        <Text style={styles.passengerAvatarText}>{item.avatar}</Text>
      </View>
      <View style={styles.passengerInfo}>
        <Text style={styles.passengerName}>{item.name}</Text>
        <Text style={styles.passengerStatus}>مؤكد</Text>
      </View>
      <View style={styles.confirmationBadge}>
        <Text style={styles.confirmationText}>{item.confirmationCode}</Text>
      </View>
    </TouchableOpacity>
  );

  const mapRegion = {
    latitude: 34.0275,
    longitude: -6.8275,
    latitudeDelta: 0.015,
    longitudeDelta: 0.015,
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.light.primary} />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <Text style={styles.backButtonIcon}>←</Text>
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>معلومات المركبة</Text>
          <Text style={styles.headerSubtitle}>Vehicle Information</Text>
        </View>
        <View style={styles.headerRight}>
          <Text style={styles.headerTime}>{currentTime}</Text>
        </View>
      </View>

      {/* Vehicle Info Card */}
      <View style={styles.vehicleInfoSection}>
        <View style={styles.vehicleInfoCard}>
          <View style={styles.vehicleHeader}>
            <Text style={styles.vehicleIcon}>🚌</Text>
            <View style={styles.vehicleDetails}>
              <Text style={styles.vehicleTitle}>
                {VEHICLE_INFO.year} {VEHICLE_INFO.make} {VEHICLE_INFO.model}
              </Text>
              <Text style={styles.vehicleSubtitle}>
                {VEHICLE_INFO.color} • {VEHICLE_INFO.licensePlate}
              </Text>
            </View>
            <View style={styles.capacityInfo}>
              <Text style={styles.capacityText}>
                {VEHICLE_INFO.currentLoad}/{VEHICLE_INFO.capacity}
              </Text>
              <Text style={styles.capacityLabel}>طلاب</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Map Section */}
      <View style={styles.mapSection}>
        <View style={styles.mapContainer}>
          <MapView
            style={styles.map}
            provider={PROVIDER_GOOGLE}
            initialRegion={mapRegion}
            showsUserLocation={true}
            showsMyLocationButton={false}
            showsCompass={false}
            toolbarEnabled={false}
          >
            {/* Route Polyline */}
            <Polyline
              coordinates={VEHICLE_ROUTE}
              strokeColor={Colors.light.primary}
              strokeWidth={4}
            />
            
            {/* Route Markers */}
            {VEHICLE_ROUTE.map((location, index) => (
              <Marker
                key={index}
                coordinate={location}
                pinColor={index === 0 ? Colors.light.warning : Colors.light.error}
                title={location.title}
              />
            ))}
          </MapView>
          
          {/* Map Overlay */}
          <View style={styles.mapOverlay}>
            <View style={styles.distanceIndicator}>
              <Text style={styles.distanceText}>{remainingDistance}KM</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Confirmed Passengers List */}
      <View style={styles.passengersSection}>
        <View style={styles.passengersSectionHeader}>
          <Text style={styles.passengersSectionTitle}>الطلاب المؤكدون</Text>
          <Text style={styles.passengersCount}>
            {CONFIRMED_PASSENGERS.length} طلاب
          </Text>
        </View>
        
        <FlatList
          data={CONFIRMED_PASSENGERS}
          renderItem={renderPassenger}
          keyExtractor={(item) => item.id.toString()}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.passengersList}
        />
      </View>

      {/* Action Button */}
      <View style={styles.actionSection}>
        <TouchableOpacity style={styles.completeButton} onPress={handleCompleteTrip}>
          <Text style={styles.completeButtonText}>إكمال الرحلة</Text>
        </TouchableOpacity>
      </View>

      {/* Navigation Bar */}
      <DriverNavigationBar currentScreen="vehicle-info" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 15,
    backgroundColor: Colors.light.primary,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonIcon: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  headerRight: {
    alignItems: 'center',
  },
  headerTime: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  vehicleInfoSection: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  vehicleInfoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  vehicleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  vehicleIcon: {
    fontSize: 32,
    marginRight: 15,
  },
  vehicleDetails: {
    flex: 1,
  },
  vehicleTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  vehicleSubtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  capacityInfo: {
    alignItems: 'center',
  },
  capacityText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.light.primary,
  },
  capacityLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  mapSection: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  mapContainer: {
    height: 180,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  map: {
    flex: 1,
  },
  mapOverlay: {
    position: 'absolute',
    top: 15,
    right: 15,
  },
  distanceIndicator: {
    backgroundColor: 'rgba(59, 130, 246, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  distanceText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  passengersSection: {
    flex: 1,
    paddingHorizontal: 20,
  },
  passengersSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  passengersSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  passengersCount: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.light.primary,
  },
  passengersList: {
    paddingBottom: 10,
  },
  passengerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  passengerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.success,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  passengerAvatarText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  passengerInfo: {
    flex: 1,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 2,
  },
  passengerStatus: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.success,
  },
  confirmationBadge: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmationText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  actionSection: {
    paddingHorizontal: 20,
    paddingBottom: 30,
    paddingTop: 10,
  },
  completeButton: {
    backgroundColor: Colors.light.success,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: Colors.light.success,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  completeButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});
